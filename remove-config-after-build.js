const fs = require('fs').promises;
const path = require('path');

// <PERSON><PERSON> s<PERSON>ch các file và thư mục cần xóa
const itemsToDelete = [
    'dist/assets/env-hou.json',
    'dist/assets/env.json',
    'dist/assets/env.empty.json',
    'dist/assets/env.production.json',
    'dist/assets/tmp/i18n/en-US-custom.json',
    'dist/assets/tmp/i18n/vi-VN-custom.json',
    'dist/assets/tmp/app-data.json',
    'dist/assets/fill',
    'dist/assets/outline',
    'dist/assets/twotone'
];

async function deleteItems(items) {
    for (const item of items) {
        try {
            const stats = await fs.stat(item);
            if (stats.isDirectory()) {
                // Xóa tất cả nội dung trong thư mục trước
                const subItems = await fs.readdir(item);
                await deleteItems(subItems.map(subItem => path.join(item, subItem)));
                // <PERSON><PERSON> <PERSON>hi thư mục trống, x<PERSON><PERSON> thư mục
                await fs.rmdir(item);
            } else {
                // Xóa file
                await fs.unlink(item);
            }
        } catch (err) {
        }
    }
}

deleteItems(itemsToDelete).then(() => {
    console.log('All specified items have been deleted.');
});