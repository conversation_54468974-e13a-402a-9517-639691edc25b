# Hướng dẫn Đa ngôn ngữ (i18n) cho API Key Management

## Tổng quan

Module API Key Management đã được cập nhật để hỗ trợ đa ngôn ngữ (internationalization - i18n) với tiếng Việt và tiếng Anh.

## Các file ngôn ngữ đã cập nhật

### 1. Tiếng <PERSON> (`src/assets/tmp/i18n/vi-VN.json`)

```json
{
  "menu.api-key": "Quản lý API Key",
  "function.api-key.search-box.placeholder": "Tìm kiếm API Key...",
  "function.api-key.table.user": "User",
  "function.api-key.table.key": "API Key",
  "function.api-key.table.description": "Mô tả",
  "function.api-key.table.valid-from": "Ngày bắt đầu",
  "function.api-key.table.valid-to": "<PERSON><PERSON><PERSON> hết hạn",
  "function.api-key.table.status": "Tr<PERSON><PERSON> thái",
  "function.api-key.table.action": "<PERSON>hao tác",

  "function.api-key.modal.form.user": "User",
  "function.api-key.modal.form.user.required": "User là bắt buộc",
  "function.api-key.modal.form.user.placeholder": "Chọn User",
  "function.api-key.modal.form.key": "API Key",
  "function.api-key.modal.form.key.placeholder": "API Key sẽ được tự động tạo",
  "function.api-key.modal.form.key.note": "API Key sẽ được tự động tạo khi lưu",
  "function.api-key.modal.form.description": "Mô tả",
  "function.api-key.modal.form.description.placeholder": "Nhập mô tả cho API Key",
  "function.api-key.modal.form.valid-from": "Ngày bắt đầu",
  "function.api-key.modal.form.valid-from.required": "Ngày bắt đầu là bắt buộc",
  "function.api-key.modal.form.valid-from.placeholder": "Chọn ngày bắt đầu",
  "function.api-key.modal.form.valid-to": "Ngày hết hạn",
  "function.api-key.modal.form.valid-to.required": "Ngày hết hạn là bắt buộc",
  "function.api-key.modal.form.valid-to.placeholder": "Chọn ngày hết hạn",
  "function.api-key.modal.form.status": "Trạng thái",
  "function.api-key.modal.form.status.active": "Hoạt động",
  "function.api-key.modal.form.status.inactive": "Không hoạt động",

  "function.api-key.confirm-delete.title": "Xác nhận xóa API Key",
  "function.api-key.confirm-delete.content": "Bạn có chắc chắn muốn xóa API Key này?",
  "function.api-key.confirm-revoke.title": "Xác nhận thu hồi API Key",
  "function.api-key.confirm-revoke.content": "Bạn có chắc chắn muốn thu hồi API Key này? API Key sẽ không thể sử dụng được nữa.",
  "function.api-key.confirm-revoke.ok": "Thu hồi",
  "function.api-key.confirm-revoke.cancel": "Hủy",

  "function.api-key.modal.title-add": "Thêm mới API Key",
  "function.api-key.modal.title-info": "Thông tin API Key",
  "function.api-key.modal.title-edit": "Chỉnh sửa API Key",
  "function.api-key.page.title": "Quản lý API Key",

  "function.api-key.message.revoke-success": "Thu hồi API Key thành công",
  "function.api-key.message.create-success": "Tạo API Key thành công",
  "function.api-key.message.update-success": "Cập nhật API Key thành công",
  "function.api-key.message.delete-success": "Xóa API Key thành công"
}
```

### 2. Tiếng Anh (`src/assets/tmp/i18n/en-US.json`)

```json
{
  "menu.api-key": "API Key Management",
  "function.api-key.search-box.placeholder": "Search API Key...",
  "function.api-key.table.user": "User",
  "function.api-key.table.key": "API Key",
  "function.api-key.table.description": "Description",
  "function.api-key.table.valid-from": "Valid From",
  "function.api-key.table.valid-to": "Valid To",
  "function.api-key.table.status": "Status",
  "function.api-key.table.action": "Action",

  "function.api-key.modal.form.user": "User",
  "function.api-key.modal.form.user.required": "User is required",
  "function.api-key.modal.form.user.placeholder": "Select User",
  "function.api-key.modal.form.key": "API Key",
  "function.api-key.modal.form.key.placeholder": "API Key will be auto-generated",
  "function.api-key.modal.form.key.note": "API Key will be auto-generated when saved",
  "function.api-key.modal.form.description": "Description",
  "function.api-key.modal.form.description.placeholder": "Enter description for API Key",
  "function.api-key.modal.form.valid-from": "Valid From",
  "function.api-key.modal.form.valid-from.required": "Valid From is required",
  "function.api-key.modal.form.valid-from.placeholder": "Select start date",
  "function.api-key.modal.form.valid-to": "Valid To",
  "function.api-key.modal.form.valid-to.required": "Valid To is required",
  "function.api-key.modal.form.valid-to.placeholder": "Select end date",
  "function.api-key.modal.form.status": "Status",
  "function.api-key.modal.form.status.active": "Active",
  "function.api-key.modal.form.status.inactive": "Inactive",

  "function.api-key.confirm-delete.title": "Confirm Delete API Key",
  "function.api-key.confirm-delete.content": "Are you sure you want to delete this API Key?",
  "function.api-key.confirm-revoke.title": "Confirm Revoke API Key",
  "function.api-key.confirm-revoke.content": "Are you sure you want to revoke this API Key? The API Key will no longer be usable.",
  "function.api-key.confirm-revoke.ok": "Revoke",
  "function.api-key.confirm-revoke.cancel": "Cancel",

  "function.api-key.modal.title-add": "Add New API Key",
  "function.api-key.modal.title-info": "API Key Information",
  "function.api-key.modal.title-edit": "Edit API Key",
  "function.api-key.page.title": "API Key Management",

  "function.api-key.message.revoke-success": "API Key revoked successfully",
  "function.api-key.message.create-success": "API Key created successfully",
  "function.api-key.message.update-success": "API Key updated successfully",
  "function.api-key.message.delete-success": "API Key deleted successfully"
}
```

## Cách sử dụng i18n trong code

### 1. Trong TypeScript Component

```typescript
// Sử dụng i18n service
this.title = this.i18n.fanyi('function.api-key.page.title');

// Trong column definitions
headerName: this.i18n.fanyi('function.api-key.table.user')

// Trong messages
this.message.success(this.i18n.fanyi('function.api-key.message.revoke-success'));
```

### 2. Trong HTML Template

```html
<!-- Sử dụng pipe i18n -->
<nz-form-label>{{ 'function.api-key.modal.form.user' | i18n }}</nz-form-label>

<!-- Trong placeholder -->
<input placeholder="{{ 'function.api-key.search-box.placeholder' | i18n }}" />

<!-- Trong error tip -->
<nz-form-control nzErrorTip="{{ 'function.api-key.modal.form.user.required' | i18n }}">
```

## Cấu trúc key i18n

### Pattern đặt tên:
```
function.api-key.[section].[element].[property]
```

### Ví dụ:
- `function.api-key.table.user` - Header cột User trong bảng
- `function.api-key.modal.form.user.required` - Message validation cho field User
- `function.api-key.confirm-delete.title` - Tiêu đề dialog xác nhận xóa

## Các phần đã được i18n

### ✅ Grid/Table
- Tất cả header columns
- Search placeholder
- Action buttons

### ✅ Modal Form
- Tất cả labels
- Tất cả placeholders
- Tất cả validation messages
- Modal titles (Add/Edit/Info)

### ✅ Confirmations
- Delete confirmation
- Revoke confirmation
- Button texts

### ✅ Messages
- Success messages
- Error messages

## Cách thêm ngôn ngữ mới

1. **Thêm file ngôn ngữ mới** (ví dụ: `ja-JP.json`)
2. **Copy structure** từ `vi-VN.json` hoặc `en-US.json`
3. **Translate** tất cả values sang ngôn ngữ mới
4. **Cấu hình** trong app settings để hỗ trợ ngôn ngữ mới

## Lưu ý

1. **Consistency**: Đảm bảo tất cả keys có trong cả 2 file ngôn ngữ
2. **Naming**: Sử dụng pattern đặt tên nhất quán
3. **Testing**: Test chuyển đổi ngôn ngữ để đảm bảo không có key nào bị thiếu
4. **Fallback**: Hệ thống sẽ fallback về tiếng Anh nếu không tìm thấy key trong ngôn ngữ hiện tại

Module API Key Management giờ đây đã hỗ trợ đầy đủ đa ngôn ngữ và sẵn sàng cho việc mở rộng thêm các ngôn ngữ khác!
