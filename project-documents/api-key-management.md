# Hướng dẫn Quản lý API Key

## Tổng quan

Module quản lý API Key được phát triển dựa trên pattern của module tôn gi<PERSON><PERSON>, cho phép quản trị viê<PERSON> t<PERSON>, chỉnh sử<PERSON>, xem và thu hồi API Key cho các user trong hệ thống.

## Cấu trúc thư mục

```
src/app/routes/system/catalog/api-key/
├── api-key/
│   ├── api-key.component.ts          # Component chính hiển thị danh sách
│   ├── api-key.component.html        # Template hiển thị grid
│   ├── api-key.component.less        # Styles
│   └── api-key.component.spec.ts     # Unit tests
└── api-key-item/
    ├── api-key-item.component.ts     # Component modal thêm/sửa/xem
    ├── api-key-item.component.html   # Template modal form
    ├── api-key-item.component.less   # Styles
    └── api-key-item.component.spec.ts # Unit tests
```

## Các file đã tạo/cập nhật

### 1. Service API
- `src/app/services/api/api-key.service.ts` - Service gọi API backend

### 2. Router Configuration
- `src/app/utils/api-router.ts` - Thêm apiKeyRouter với các endpoint
- `src/app/routes/system/system-routing.module.ts` - Thêm route '/api-key'

### 3. Module Configuration
- `src/app/routes/system/system.module.ts` - Đăng ký components

### 4. Components
- `ApiKeyComponent` - Component chính hiển thị danh sách API Key
- `ApiKeyItemComponent` - Component modal để thêm/sửa/xem chi tiết

## Tính năng chính

### 1. Hiển thị danh sách API Key
- Grid hiển thị với các cột: User ID, API Key, Mô tả, Ngày bắt đầu, Ngày hết hạn, Trạng thái
- Phân trang và tìm kiếm
- Sắp xếp theo các cột

### 2. Thêm mới API Key
- Form nhập thông tin: User ID, Mô tả, Ngày bắt đầu, Ngày hết hạn, Thứ tự, Trạng thái
- API Key sẽ được tự động tạo bởi backend
- Validation các trường bắt buộc

### 3. Chỉnh sửa API Key
- Cho phép sửa tất cả thông tin trừ API Key
- Validation dữ liệu đầu vào

### 4. Xem chi tiết API Key
- Hiển thị tất cả thông tin ở chế độ readonly

### 5. Xóa API Key
- Xác nhận trước khi xóa
- Xóa vĩnh viễn khỏi hệ thống

### 6. Thu hồi API Key
- Tính năng đặc biệt để vô hiệu hóa API Key
- API Key sẽ không thể sử dụng được nữa nhưng vẫn lưu trong hệ thống

## API Endpoints

Dựa trên swagger, module sử dụng các endpoint sau:

```
POST   /system/v1/api-key           # Tạo mới API Key
PUT    /system/v1/api-key/{id}      # Cập nhật API Key
DELETE /system/v1/api-key/{id}      # Xóa API Key
GET    /system/v1/api-key/{id}      # Lấy thông tin API Key
PUT    /system/v1/api-key/{id}/revoke # Thu hồi API Key
POST   /system/v1/api-key/filter    # Lấy danh sách có phân trang
GET    /system/v1/api-key/combobox  # Lấy danh sách cho combobox
```

## Models

### CreateApiKeyModel
```typescript
{
  id: number,
  userId: number,
  validFrom: Date,
  validTo: Date,
  key: string,
  isActive: boolean,
  order: number,
  description: string,
  createdDate: Date,
  createdUserId: number
}
```

### UpdateApiKeyModel
```typescript
{
  id: number,
  userId: number,
  validFrom: Date,
  validTo: Date,
  key: string,
  isActive: boolean,
  order: number,
  description: string,
  createdDate: Date,
  modifiedUserId: number
}
```

### ApiKeyModel
```typescript
{
  id: number,
  userId: number,
  validFrom: Date,
  validTo: Date,
  key: string,
  isActive: boolean,
  order: number,
  description: string,
  createdDate: Date,
  createdUserId: number,
  modifiedUserId: number,
  modifiedDate: Date
}
```

## Quyền truy cập

Module sử dụng các quyền sau (cần cấu hình trong hệ thống ACL):
- `API_KEY_ADD` - Quyền thêm mới API Key
- `API_KEY_EDIT` - Quyền chỉnh sửa API Key
- `API_KEY_DELETE` - Quyền xóa API Key

## Cách sử dụng

### 1. Truy cập module
- Đăng nhập vào hệ thống
- Vào menu System > API Key Management
- URL: `/sys/api-key`

### 2. Thêm mới API Key
- Click nút "Thêm mới"
- Nhập thông tin User ID (bắt buộc)
- Nhập mô tả
- Chọn ngày bắt đầu và ngày hết hạn (bắt buộc)
- Thiết lập thứ tự và trạng thái
- Click "Lưu"

### 3. Chỉnh sửa API Key
- Click vào nút "Sửa" trên dòng cần chỉnh sửa
- Hoặc double-click vào dòng, sau đó click "Sửa"
- Thay đổi thông tin cần thiết
- Click "Lưu"

### 4. Thu hồi API Key
- Click vào nút "Thu hồi" trên dòng API Key đang hoạt động
- Xác nhận thu hồi
- API Key sẽ bị vô hiệu hóa

### 5. Xóa API Key
- Chọn một hoặc nhiều dòng cần xóa
- Click nút "Xóa"
- Xác nhận xóa

## Lưu ý kỹ thuật

1. **Pattern Design**: Module được thiết kế theo pattern của module tôn giáo để đảm bảo tính nhất quán
2. **Validation**: Sử dụng Angular Reactive Forms với validation
3. **UI Framework**: Sử dụng NG-ZORRO cho giao diện
4. **Grid**: Sử dụng AG-Grid cho hiển thị danh sách
5. **Responsive**: Giao diện responsive, hỗ trợ mobile
6. **Internationalization**: Sẵn sàng cho đa ngôn ngữ (hiện tại tiếng Việt)

## Tích hợp với hệ thống

Module đã được tích hợp đầy đủ vào hệ thống:
- Routing đã được cấu hình
- Service đã được tạo
- Components đã được đăng ký trong module
- API endpoints đã được định nghĩa

Để sử dụng, chỉ cần đảm bảo backend API đã được implement theo đúng swagger specification.
