# Hướng dẫn Quản lý Chứng thư số

## Tổng quan

Module quản lý Chứng thư số được phát triển để quản lý các chứng thư số điện tử trong hệ thống. Mo<PERSON><PERSON> này cho phép quản trị viên thêm, s<PERSON><PERSON>, x<PERSON><PERSON>, xem và quản lý các chứng thư số của người dùng.

## Cấu trúc thư mục

```
src/app/routes/system/catalog/chung-thu-so/
├── chung-thu-so/
│   ├── chung-thu-so.component.ts          # Component chính hiển thị danh sách
│   ├── chung-thu-so.component.html        # Template hiển thị grid
│   ├── chung-thu-so.component.less        # Styles
│   └── chung-thu-so.component.spec.ts     # Unit tests
├── chung-thu-so-item/
│   ├── chung-thu-so-item.component.ts     # Component modal thêm/sửa/xem
│   ├── chung-thu-so-item.component.html   # Template modal form
│   ├── chung-thu-so-item.component.less   # Styles
│   └── chung-thu-so-item.component.spec.ts # Unit tests
└── visnam-tai-khoan-ket-noi/
    ├── visnam-tai-khoan-ket-noi.component.ts     # Component popup quản lý tài khoản Visnam
    ├── visnam-tai-khoan-ket-noi.component.html   # Template popup với grid và form
    ├── visnam-tai-khoan-ket-noi.component.less   # Styles
    └── visnam-tai-khoan-ket-noi.component.spec.ts # Unit tests
```

## API Endpoints

Module sử dụng các API endpoints sau:

- `POST /system/v1/chung-thu-so/filter` - Lấy danh sách chứng thư số có phân trang
- `GET /system/v1/chung-thu-so/{id}` - Lấy thông tin chi tiết chứng thư số
- `POST /system/v1/chung-thu-so` - Tạo mới chứng thư số
- `PUT /system/v1/chung-thu-so/{id}` - Cập nhật chứng thư số
- `DELETE /system/v1/chung-thu-so/{id}` - Xóa chứng thư số
- `GET /system/v1/chung-thu-so/for-combobox` - Lấy danh sách cho combobox
- `GET /system/v1/chung-thu-so/for-user` - Lấy danh sách chứng thư số của người dùng
- `GET /system/v1/chung-thu-so/statistics` - Lấy thống kê chứng thư số
- `POST /system/v1/chung-thu-so/bulk` - Tạo nhiều chứng thư số cùng lúc

### API Endpoints cho VisnamTaiKhoanKetNoi

- `POST /system/v1/visnam-tai-khoan-ket-noi/filter` - Lấy danh sách tài khoản kết nối có phân trang
- `GET /system/v1/visnam-tai-khoan-ket-noi/{id}` - Lấy thông tin chi tiết tài khoản kết nối
- `POST /system/v1/visnam-tai-khoan-ket-noi` - Tạo mới tài khoản kết nối
- `PUT /system/v1/visnam-tai-khoan-ket-noi/{id}` - Cập nhật tài khoản kết nối
- `DELETE /system/v1/visnam-tai-khoan-ket-noi/{id}` - Xóa tài khoản kết nối
- `GET /system/v1/visnam-tai-khoan-ket-noi/for-combobox` - Lấy danh sách cho combobox
- `POST /system/v1/visnam-tai-khoan-ket-noi/sync-certificates/{id}` - Đồng bộ chứng thư số từ Visnam

## Cấu trúc dữ liệu

### ChungThuSoModel
```typescript
{
  id: number;                    // ID chứng thư số
  serialNumber: string;          // Số serial (bắt buộc, tối đa 100 ký tự)
  subjectName: string;           // Tên chủ thể (bắt buộc, tối đa 500 ký tự)
  issuer: string;                // Nhà phát hành (bắt buộc, tối đa 50 ký tự)
  certificateBase64: string;     // Nội dung chứng thư số dạng Base64 (bắt buộc)
  notBefore: Date;               // Ngày bắt đầu hiệu lực
  notAfter: Date;                // Ngày kết thúc hiệu lực
  source: number;                // Nguồn (0: Hệ thống, 1: Người dùng, 2: Import)
  isActive: boolean;             // Trạng thái hoạt động
  order: number;                 // Thứ tự hiển thị
  userId: number;                // ID người dùng sở hữu
  referenceId: number;           // ID tham chiếu
  createdDate: Date;             // Ngày tạo
}
```

### VisnamTaiKhoanKetNoiModel
```typescript
{
  id: number;                    // ID tài khoản kết nối
  userId: number;                // ID người dùng (bắt buộc)
  key: string;                   // Key kết nối (bắt buộc, tối đa 500 ký tự)
  secret: string;                // Secret kết nối (bắt buộc, tối đa 500 ký tự)
  isActive: boolean;             // Trạng thái hoạt động
  order: number;                 // Thứ tự hiển thị
  createdDate: Date;             // Ngày tạo
  userName: string;              // Tên người dùng (chỉ đọc)
}
```

## Chức năng chính

### 1. Hiển thị danh sách chứng thư số
- Hiển thị danh sách chứng thư số trong bảng với các cột:
  - STT
  - Checkbox chọn
  - Số serial
  - Tên chủ thể
  - Nhà phát hành
  - Có hiệu lực từ
  - Có hiệu lực đến
  - Trạng thái
  - Thao tác (Xem/Sửa/Xóa)

### 2. Tìm kiếm và lọc
- Tìm kiếm theo số serial, tên chủ thể, nhà phát hành
- Phân trang với các tùy chọn số bản ghi trên trang

### 3. ~~Thêm mới chứng thư số~~ (Đã vô hiệu hóa)
- ~~Form nhập thông tin chứng thư số~~
- ~~Upload file chứng thư số (.cer, .crt, .p12, .pfx)~~
- **Chỉ cho phép đồng bộ chứng thư số từ hệ thống Visnam**
- Hiển thị thông báo hướng dẫn sử dụng chức năng đồng bộ

### 4. Chỉnh sửa chứng thư số (Hạn chế)
- **Chỉ cho phép cập nhật trường `isActive` (trạng thái)**
- Các trường khác hiển thị ở chế độ chỉ đọc
- Form hiển thị thông tin chi tiết dạng descriptions

### 5. Xem chi tiết chứng thư số
- Hiển thị thông tin chi tiết ở chế độ chỉ đọc
- Có thể chuyển sang chế độ chỉnh sửa

### 6. Xóa chứng thư số
- Xóa một hoặc nhiều chứng thư số
- Xác nhận trước khi xóa
- Hiển thị kết quả xóa

### 7. Quản lý tài khoản kết nối Visnam
- Nút "Quản lý tài khoản Visnam" mở popup quản lý tài khoản kết nối
- Hiển thị danh sách tài khoản kết nối với thông tin:
  - Tên người dùng
  - Key (hiển thị đầy đủ)
  - Secret (ẩn, chỉ hiển thị 4 ký tự cuối)
  - Trạng thái
  - Thao tác (Xem/Sửa/Xóa/Đồng bộ)

### 8. Thêm/Sửa tài khoản kết nối Visnam
- Form nhập thông tin tài khoản:
  - ID người dùng (bắt buộc)
  - Key (bắt buộc, tối đa 500 ký tự)
  - Secret (bắt buộc, tối đa 500 ký tự, hiển thị dạng password)
  - Thứ tự hiển thị
  - Trạng thái hoạt động

### 9. Đồng bộ chứng thư số từ Visnam
- Nút "Đồng bộ" cho từng tài khoản kết nối
- Xác nhận trước khi thực hiện đồng bộ
- Hiển thị kết quả đồng bộ
- Tự động reload danh sách chứng thư số sau khi đồng bộ thành công

## Routing

Thêm route vào `system-routing.module.ts`:
```typescript
{ path: 'chung-thu-so', component: ChungThuSoComponent }
```

URL truy cập: `/sys/chung-thu-so`

## Service API

Service `ChungThuSoService` cung cấp các phương thức:
- `getFilter(model: QueryFilerModel)` - Lấy danh sách có phân trang
- `getById(id: string)` - Lấy thông tin chi tiết
- `create(model: any)` - Tạo mới
- `update(id: any, model: any)` - Cập nhật
- `delete(id: any)` - Xóa
- `getCombobox()` - Lấy danh sách cho combobox
- `getForUser(count?: number)` - Lấy danh sách cho người dùng
- `getStatistics()` - Lấy thống kê
- `createBulk(model: any)` - Tạo nhiều bản ghi

Service `VisnamTaiKhoanKetNoiService` cung cấp các phương thức:
- `getFilter(model: QueryFilerModel)` - Lấy danh sách tài khoản kết nối có phân trang
- `getById(id: string)` - Lấy thông tin chi tiết tài khoản kết nối
- `create(model: any)` - Tạo mới tài khoản kết nối
- `update(id: any, model: any)` - Cập nhật tài khoản kết nối
- `delete(id: any)` - Xóa tài khoản kết nối
- `getCombobox(count?: number)` - Lấy danh sách cho combobox
- `syncCertificates(id: any)` - Đồng bộ chứng thư số từ Visnam

## Validation

### Trường bắt buộc:
- Số serial (tối đa 100 ký tự)
- Tên chủ thể (tối đa 500 ký tự)
- Nhà phát hành (tối đa 50 ký tự)
- File chứng thư số

### Trường tùy chọn:
- Ngày có hiệu lực từ/đến
- Nguồn (mặc định: 0)
- Trạng thái (mặc định: true)
- Thứ tự (mặc định: 1)
- ID người dùng
- ID tham chiếu

## Lưu ý kỹ thuật

1. **Hạn chế thêm mới**: Không cho phép thêm mới chứng thư số trực tiếp, chỉ đồng bộ từ Visnam
2. **Chỉnh sửa hạn chế**: Chỉ cho phép cập nhật trường `isActive`, các trường khác chỉ đọc
3. **UI/UX**: Sử dụng nz-descriptions để hiển thị thông tin chi tiết
4. **Date Handling**: Sử dụng ISO string format cho ngày tháng
5. **Error Handling**: Xử lý lỗi và hiển thị thông báo phù hợp
6. **Loading States**: Hiển thị trạng thái loading khi thực hiện các thao tác
7. **Hướng dẫn người dùng**: Hiển thị alert thông báo cách thêm chứng thư số

## Quyền truy cập

Module có thể tích hợp với hệ thống phân quyền ACL:
- `CHUNG_THU_SO_VIEW` - Xem danh sách
- `CHUNG_THU_SO_ADD` - Thêm mới
- `CHUNG_THU_SO_EDIT` - Chỉnh sửa
- `CHUNG_THU_SO_DELETE` - Xóa

## Menu Navigation

Thêm vào menu trong `constants.ts`:
```typescript
{
  text: 'Chứng thư số',
  i18n: 'menu.chung-thu-so',
  link: '/sys/chung-thu-so',
  acl: ['CHUNG_THU_SO_VIEW']
}
```
