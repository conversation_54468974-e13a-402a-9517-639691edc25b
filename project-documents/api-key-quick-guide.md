# Hướng dẫn nhanh - Quản lý API Key

## Tóm tắt

Đã tạo thành công module quản lý API Key theo user dựa trên pattern của module tôn giáo.

## Files đã tạo

### 1. Service
- `src/app/services/api/api-key.service.ts`

### 2. Components
- `src/app/routes/system/catalog/api-key/api-key/api-key.component.ts`
- `src/app/routes/system/catalog/api-key/api-key/api-key.component.html`
- `src/app/routes/system/catalog/api-key/api-key/api-key.component.less`
- `src/app/routes/system/catalog/api-key/api-key/api-key.component.spec.ts`

### 3. Modal Component
- `src/app/routes/system/catalog/api-key/api-key-item/api-key-item.component.ts`
- `src/app/routes/system/catalog/api-key/api-key-item/api-key-item.component.html`
- `src/app/routes/system/catalog/api-key/api-key-item/api-key-item.component.less`
- `src/app/routes/system/catalog/api-key/api-key-item/api-key-item.component.spec.ts`

## Files đã cập nhật

### 1. Router & API
- `src/app/utils/api-router.ts` - Thêm apiKeyRouter
- `src/app/routes/system/system-routing.module.ts` - Thêm route api-key
- `src/app/routes/system/system.module.ts` - Đăng ký components

## Tính năng

✅ **CRUD Operations**
- Tạo mới API Key
- Xem danh sách với phân trang
- Chỉnh sửa thông tin
- Xóa API Key

✅ **Tính năng đặc biệt**
- Combobox chọn User (thay vì nhập User ID)
- Hiển thị User Name trong grid
- Tìm kiếm
- Hiển thị trạng thái
- Format ngày tháng dd/MM/yyyy HH:mm:ss

✅ **UI/UX**
- Grid hiển thị với AG-Grid
- Modal form với NG-ZORRO
- User selection với search và clear
- Responsive design
- Validation form
- **Đa ngôn ngữ (i18n)**: Hỗ trợ tiếng Việt và tiếng Anh

## Truy cập

**URL:** `/sys/api-key`

**Menu:** System > API Key Management

## Quyền cần thiết

- `API_KEY_ADD` - Thêm mới
- `API_KEY_EDIT` - Chỉnh sửa  
- `API_KEY_DELETE` - Xóa

## API Endpoints sử dụng

```
POST   /system/v1/api-key           # Tạo mới
PUT    /system/v1/api-key/{id}      # Cập nhật
DELETE /system/v1/api-key/{id}      # Xóa
GET    /system/v1/api-key/{id}      # Chi tiết
PUT    /system/v1/api-key/{id}/revoke # Thu hồi
POST   /system/v1/api-key/filter    # Danh sách
GET    /system/v1/api-key/combobox  # Combobox
```

## Cấu trúc dữ liệu chính

```typescript
interface ApiKey {
  id: number;
  userId: number;
  key: string;
  description: string;
  validFrom: Date;
  validTo: Date;
  isActive: boolean;
  order: number;
  createdDate: Date;
  createdUserId: number;
  modifiedDate: Date;
  modifiedUserId: number;
}
```

## Lưu ý

1. **API Key tự động tạo**: Khi tạo mới, API Key sẽ được backend tự động generate
2. **Không sửa Key**: Khi chỉnh sửa, không thể thay đổi API Key
3. **User Selection**: Sử dụng combobox để chọn user, có tính năng search và clear
4. **Hiển thị User**: Grid hiển thị User Name thay vì User ID
5. **Format ngày**: Ngày tháng hiển thị theo định dạng dd/MM/yyyy HH:mm:ss
6. **Đa ngôn ngữ**: Hỗ trợ tiếng Việt và tiếng Anh, tự động chuyển đổi theo cài đặt hệ thống
7. **Validation**: User, ngày bắt đầu và ngày hết hạn là bắt buộc

## Kiểm tra

Để kiểm tra module hoạt động:

1. Đảm bảo backend API đã implement theo swagger
2. Cấu hình quyền truy cập trong ACL
3. Truy cập `/sys/api-key` để test giao diện
4. Test các chức năng CRUD và revoke

Module đã sẵn sàng sử dụng!
