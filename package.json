{"name": "ng-alain-pro", "version": "0.0.0", "description": "<PERSON>-alain business theme, ng-zorro-antd admin panel front-end framework", "author": "cipchk <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/ng-alain/ng-alain.git"}, "homepage": "https://e.ng-alain.com/theme/pro", "scripts": {"prebuild": "node update-environment.js", "ng-high-memory": "node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng", "ng": "ng", "start": "ng s -o --port 8102", "start-local": "ng s -c local -o --port 8102", "start-test": "ng s -c test -o --port 8102", "hmr": "ng s -o --hmr", "build": "node update-environment.js && npm run ng-high-memory build  && node remove-config-after-build.js", "build-origin": "node update-environment.js && npm run ng-high-memory build", "analyze": "npm run ng-high-memory build -- --source-map", "analyze:view": "source-map-explorer dist/**/*.js", "lint": "npm run lint:ts && npm run lint:style", "lint:ts": "ng lint --fix", "lint:style": "npx stylelint 'src/**/*.less'", "e2e": "ng e2e", "test": "ng test --watch", "test-coverage": "ng test --code-coverage --watch=false", "color-less": "ng-alain-plugin-theme -t=colorLess", "theme": "ng-alain-plugin-theme -t=themeCss", "icon": "ng g ng-alain:plugin icon"}, "dependencies": {"@ag-grid-community/core": "^29.1.0", "@ag-grid-enterprise/core": "^29.1.0", "@angular/animations": "^15.0.0", "@angular/common": "^15.0.0", "@angular/compiler": "^15.0.0", "@angular/core": "^15.0.0", "@angular/forms": "^15.0.0", "@angular/google-maps": "^15.0.4", "@angular/platform-browser": "^15.0.0", "@angular/platform-browser-dynamic": "^15.0.0", "@angular/router": "^15.0.0", "@auth0/angular-jwt": "^5.1.2", "@ckeditor/ckeditor5-angular": "^7.0.1", "@ckeditor/ckeditor5-build-classic": "^41.2.0", "@ckeditor/ckeditor5-core": "^41.2.0", "@ckeditor/ckeditor5-engine": "^41.2.0", "@ckeditor/ckeditor5-utils": "^41.2.0", "@ckeditor/ckeditor5-watchdog": "^41.2.0", "@delon/abc": "^15.0.0", "@delon/acl": "^15.0.0", "@delon/auth": "^15.0.0", "@delon/cache": "^15.0.0", "@delon/chart": "^15.0.0", "@delon/form": "^15.0.0", "@delon/mock": "^15.0.0", "@delon/theme": "^15.0.0", "@delon/util": "^15.0.0", "@fullcalendar/core": "^6.0.2", "@fullcalendar/daygrid": "^6.0.2", "@fullcalendar/interaction": "^6.0.2", "@fullcalendar/list": "^6.0.2", "@fullcalendar/timegrid": "^6.0.2", "@microsoft/signalr": "^8.0.0", "@optimajet/workflow-designer-angular": "^9.1.1", "@swimlane/ngx-charts": "^20.1.2", "ag-grid-angular": "^29.1.0", "ag-grid-community": "^29.1.0", "ag-grid-enterprise": "^29.1.0", "angular-baidu-maps": "^12.0.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "js-base64": "^3.7.4", "masonry-layout": "^4.2.2", "moment": "^2.29.4", "ng-gallery": "^8.0.1", "ng-zorro-antd": "^15.0.0", "ngx-trend": "^8.0.0", "oidc-client": "^1.11.5", "perfect-scrollbar": "^1.5.5", "quill": "^1.3.7", "quill-image-resize-module": "^3.0.0", "rxjs": "~7.5.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.0.4", "@angular-eslint/builder": "~15.1.0", "@angular-eslint/eslint-plugin": "~15.1.0", "@angular-eslint/eslint-plugin-template": "~15.1.0", "@angular-eslint/schematics": "~15.1.0", "@angular-eslint/template-parser": "~15.1.0", "@angular/cli": "~15.0.4", "@angular/compiler-cli": "^15.0.0", "@angular/language-service": "^15.0.4", "@delon/testing": "^15.0.0", "@types/d3": "^7.4.0", "@types/file-saver": "^2.0.5", "@types/jasmine": "~4.3.0", "@types/jasminewd2": "~2.0.10", "@types/js-base64": "^3.3.1", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "~5.47.1", "@typescript-eslint/parser": "~5.47.1", "eslint": "^8.31.0", "eslint-config-prettier": "~8.5.0", "eslint-plugin-deprecation": "~1.3.3", "eslint-plugin-import": "~2.26.0", "eslint-plugin-jsdoc": "~39.6.4", "eslint-plugin-prefer-arrow": "~1.2.3", "eslint-plugin-prettier": "~4.2.1", "jasmine-core": "~4.5.0", "jasmine-spec-reporter": "^7.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "lint-staged": "^13.1.0", "ng-alain": "^15.0.0", "node-fetch": "^2.6.1", "prettier": "^2.8.1", "protractor": "~7.0.0", "source-map-explorer": "^2.5.3", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.4", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^29.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.6.0", "stylelint-order": "^5.0.0", "ts-node": "~10.9.1", "typescript": "~4.8.2"}, "private": true, "theme": {"name": "pro", "version": "15.0.0"}}