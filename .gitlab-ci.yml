image: thienanunisoft/angular-node:18.20.8

services:
  - docker:dind

variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_TLS_CERTDIR: ""
  DOCKER_DRIVER: overlay2

  # Docker info
  IMAGE_NAME_TAG_TEST: thienanunisoft/system-fe:test
  IMAGE_NAME_TAG_LATEST: thienanunisoft/system-fe:latest
  DOCKER_FILE: Dockerfile.builded
  # MinIO info
  MINIO_PATH: uni-system
  ZIP_FILE_NAME: uni-system-ui.zip
  ZIP_FILE_NAME_RM_CONFIG: uni-system-ui-remove-config-file.zip

stages:
  - build

# Build image and push to Docker Hub branch test
build_and_push_test:
  stage: build

  before_script:
    - git config --global url."https://$GITLAB_CI_USER:$<EMAIL>/".insteadOf "https://gitlab.unisoft.edu.vn/"
    - docker --version  # 🧪 Kiểm tra docker đã hoạt động
    - node --version  # 🧪 Kiểm tra dotnet đã hoạt động
    - npm -v
    - echo "Logging into Docker Hub..."
    - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
    - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
    - docker pull $IMAGE_NAME_TAG_TEST || true

  script:
    - git submodule update --init --recursive
    # Build ứng dụng
    - npm install
    - node update-environment.js
    - npm run ng-high-memory build
    - echo "Build UI project completed."

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_TEST . -f $DOCKER_FILE
    - docker push $IMAGE_NAME_TAG_TEST
    - echo "Docker image pushed to Docker Hub."

    - echo "Uploading files via ncftpput..."
    - ncftpput -R -v -u "$FTP_USER" -p "$FTP_PASS" "$FTP_URL" /UniSystemAPPWeb/build/ ./dist/

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: $CI_PROJECT_NAME-cache
    paths:
      - node_modules

  only:
    - test

# Build image and push to Docker Hub branch test
build_and_push_latest:
  stage: build

  before_script:
    - git config --global url."https://$GITLAB_CI_USER:$<EMAIL>/".insteadOf "https://gitlab.unisoft.edu.vn/"
    - docker --version  # 🧪 Kiểm tra docker đã hoạt động
    - node --version  # 🧪 Kiểm tra dotnet đã hoạt động
    - npm -v
    - echo "Logging into Docker Hub..."
    - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
    - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
    - docker pull $IMAGE_NAME_TAG_LATEST || true

  script:
    - git submodule update --init --recursive
    # Build ứng dụng
    - npm install
    - node update-environment.js
    - npm run ng-high-memory build
    - echo "Build UI project completed."

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_LATEST . -f $DOCKER_FILE
    - docker push $IMAGE_NAME_TAG_LATEST
    - echo "Docker image pushed to Docker Hub."

    # Thêm file web.config vào thư mục dist
    - cp ./file/web.config ./dist/web.config

    # Zip file and upload to Minio
    - zip -r $ZIP_FILE_NAME ./dist
    
    # Xóa file web.config khỏi thư mục dist
    - rm ./dist/web.config

    # Xóa file cấu hình môi trường
    - rm -f ./dist/assets/env*.json

    # Xóa các file ảnh (ico, png, jpg, jpeg, gif, bmp, svg) nếu có
    - find ./dist/ -type f \( -iname "*.ico" -o -iname "*.png" -o -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.gif" -o -iname "*.bmp" -o -iname "*.svg" \) -delete

    - node remove-config-after-build.js
    - zip -r $ZIP_FILE_NAME_RM_CONFIG ./dist

    - echo "Copying $ZIP_FILE_NAME to MinIO..."
    - mc cp $ZIP_FILE_NAME $MINIO_ALIAS/$MINIO_BUCKET/$MINIO_PATH/

    - echo "Copying $ZIP_FILE_NAME_RM_CONFIG to MinIO..."
    - mc cp $ZIP_FILE_NAME_RM_CONFIG $MINIO_ALIAS/$MINIO_BUCKET/$MINIO_PATH/

    # Tạo VersionID dạng yyyyMMddHH
    - VERSION_ID=$(date +"%Y%m%d%H")

    # Ghi nội dung file update
    - |
      cat <<EOF > ./dist/UnisoftServerUpdate.txt
      [UNISOFT_UPDATE]
      VersionID=$VERSION_ID
      EOF

    - echo "Uploading files via ncftpput..."
    - ncftpput -R -v -u "$FTP_USER" -p "$FTP_PASS" "$FTP_URL" /unisoft-deploy/UniSystemAPPWeb/ ./dist/*

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: $CI_PROJECT_NAME-cache
    paths:
      - node_modules

  only:
    - master
