<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1000px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="IdCap">{{ 'function.handle.modal.form.ten-cap' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.handle.modal.form.ten-cap.required' | i18n }}">
          <nz-select
            id="IdCap"
            formControlName="IdCap"
            nzAllowClear
            [nzPlaceHolder]="'function.handle.modal.form.ten-cap.place-holder' | i18n"
            class="custom-select"
          >
            <nz-option *ngFor="let p of listCapKhenthuongKyLuat" [nzValue]="p.idCap" [nzLabel]="p.tenCap"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="XuLy">{{ 'function.handle.modal.form.name' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.handle.modal.form.name.required' | i18n }}">
          <input nz-input formControlName="XuLy" id="XuLy" placeholder="{{ 'function.handle.modal.form.name.place-holder' | i18n }}" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="SoThang">{{ 'function.handle.modal.form.so-thang' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.handle.modal.form.so-thang.required' | i18n }}">
          <input
            nz-input
            type="number"
            formControlName="SoThang"
            id="SoThang"
            placeholder="{{ 'function.handle.modal.form.so-thang.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="DiemPhat">{{
          'function.handle.modal.form.diem-phat' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.handle.modal.form.diem-phat.required' | i18n }}">
          <input
            nz-input
            type="number"
            formControlName="DiemPhat"
            id="DiemPhat"
            placeholder="{{ 'function.handle.modal.form.diem-phat.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="MucXuLy">{{ 'function.handle.modal.form.muc-xu-ly' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.handle.modal.form.muc-xu-ly.required' | i18n }}">
          <input
            nz-input
            type="number"
            formControlName="MucXuLy"
            id="MucXuLy"
            placeholder="{{ 'function.handle.modal.form.muc-xu-ly.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="primary"
      class="btn-secondary"
      *ngIf="isInfo && btnEdit.visible && btnEdit.grandAccess"
      (click)="btnEdit.click($event)"
    >
      <i nz-icon nzType="edit" nzTheme="fill"></i>{{ btnEdit.title }}
    </button>
    <button
      nz-button
      nzType="primary"
      class="btn-primary"
      *ngIf="!isInfo && btnSave.visible && btnSave.grandAccess"
      [nzLoading]="isLoading"
      (click)="btnSave.click($event)"
    >
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
