import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { log } from '@delon/util';
import { ButtonModel } from '@model';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';
import { HeDaoTaoService } from 'src/app/services/api/he-dao-tao.service';
import { LoaiRenLuyenService } from 'src/app/services/api/loai-ren-luyen.service';
import { XepLoaiHocTapThangDiem10ApiService } from 'src/app/services/api/xep-loai-hoc-tap-thang-10.service';

@Component({
  selector: 'app-xep-loai-hoc-tap-thang-10-item',
  templateUrl: './xep-loai-hoc-tap-thang-10-item.component.html',
  styleUrls: ['./xep-loai-hoc-tap-thang-10-item.component.less']
})
export class XepLoaiHocTapThangDiem10ItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  isInfo = false;
  isEdit = false;
  isAdd = false;
  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  public Editor = ClassicEditor;

  config = {
    toolbar: {
      shouldNotGroupWhenFull: true
    }
  };
  notification: any;
  listHe: any;
  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private xepLoaiHocTapThangDiem10ApiService: XepLoaiHocTapThangDiem10ApiService,
    private heDaoTaoService: HeDaoTaoService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,

      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: this.i18n.fanyi('app.common.button.edit'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      xepLoai: [null, [Validators.required]],
      tuDiem: [null, [Validators.required]],
      denDiem: [null, [Validators.required]],
      maXepLoai: [null, [Validators.required]],
      xepLoaiEn: null,
      idHe: [null, [Validators.required]]
    });
  }

  onEditorReady(editor: any): void {
    log('Editor is ready to use!', editor);
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.getHe();
    this.initRightOfUser();
  }

  initRightOfUser(): void {
    this.btnSave.grandAccess =
      this.aclService.canAbility('XEP_LOAI_HOC_TAP_THANG_DIEM_10_ADD') || this.aclService.canAbility('XEP_LOAI_HOC_TAP_THANG_DIEM_10_EDIT');
    this.btnEdit.grandAccess = this.aclService.canAbility('XEP_LOAI_HOC_TAP_THANG_DIEM_10_EDIT');
  }

  //#region Update-form-type
  updateFormToAdd(): void {
    this.isInfo = false;
    this.isEdit = false;
    this.isAdd = true;
    this.tittle = this.i18n.fanyi('function.xep-loai-hoc-tap-thang-10.modal.title-add');
    this.item = {};
    this.form.get('xepLoai')?.enable();
    this.form.get('tuDiem')?.enable();
    this.form.get('denDiem')?.enable();
    this.form.get('maXepLoai')?.enable();
    this.form.get('xepLoaiEn')?.enable();
    this.form.get('idHe')?.enable();
  }

  updateFormToInfo(): void {
    this.isInfo = true;
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.xep-loai-hoc-tap-thang-10.modal.title-info');
    this.form.get('xepLoai')?.disable();
    this.form.get('tuDiem')?.disable();
    this.form.get('denDiem')?.disable();
    this.form.get('maXepLoai')?.disable();
    this.form.get('xepLoaiEn')?.disable();
    this.form.get('idHe')?.disable();
  }

  updateFormToEdit(): void {
    this.isInfo = false;
    this.isEdit = true;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.xep-loai-hoc-tap-thang-10.modal.title-edit');
    this.form.get('xepLoai')?.enable();
    this.form.get('tuDiem')?.enable();
    this.form.get('denDiem')?.enable();
    this.form.get('maXepLoai')?.enable();
    this.form.get('xepLoaiEn')?.enable();
    this.form.get('idHe')?.enable();
  }

  resetForm(): void {
    this.form.reset();
    this.form.get('isActive')?.setValue(true);
    this.form.get('order')?.setValue(1);
    this.form.get('isHighPriority')?.setValue(false);
  }

  updateDataToForm(data: any): void {
    this.form.get('xepLoai')?.setValue(data.xepLoai);
    this.form.get('tuDiem')?.setValue(data.tuDiem);
    this.form.get('denDiem')?.setValue(data.denDiem);
    this.form.get('maXepLoai')?.setValue(data.maXepLoai);
    this.form.get('xepLoaiEn')?.setValue(data.xepLoaiEn);
    this.form.get('idHe')?.setValue(data.idHe);
  }

  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.resetForm();
    this.isLoading = false;
    this.item = data;
    this.type = type;
    this.option = option;
    if (this.item?.idXepLoai) {
      this.getDataInfo(this.item.idXepLoai);
    }
    switch (type) {
      case FORM_TYPE.ADD:
        this.updateFormToAdd();
        break;
      case FORM_TYPE.INFO:
        this.updateFormToInfo();
        break;
      case FORM_TYPE.EDIT:
        this.updateFormToEdit();
        break;
      default:
        this.updateFormToAdd();
        break;
    }
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  getDataInfo(id: any): Subscription | undefined {
    this.isLoading = true;
    const rs = this.xepLoaiHocTapThangDiem10ApiService.getById(id).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateDataToForm(res.data);
      },
      error: (err: any) => {
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      }
    });
    return rs;
  }
  getHe(): Subscription | undefined {
    const rs = this.heDaoTaoService.getCombobox().subscribe({
      next: (res: any) => {
        if (res.data === null || res.data === undefined) {
        }
        this.listHe = res.data;
      },
      error: (err: any) => {},
      complete: () => {}
    });
    return rs;
  }

  save(): Subscription | undefined {
    this.isLoading = true;

    cleanForm(this.form);
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid) {
      this.isLoading = false;
      this.messageService.error(this.i18n.fanyi('app.common.form.dirty'));
      return;
    }
    const data = {
      xepLoai: this.form.get('xepLoai')?.value,
      tuDiem: this.form.get('tuDiem')?.value,
      denDiem: this.form.get('denDiem')?.value,
      maXepLoai: this.form.get('maXepLoai')?.value,
      xepLoaiEn: this.form.get('xepLoaiEn')?.value,
      idHe: this.form.get('idHe')?.value
    };

    if (this.isAdd) {
      const promise = this.xepLoaiHocTapThangDiem10ApiService.create(data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }

          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else if (this.isEdit) {
      const promise = this.xepLoaiHocTapThangDiem10ApiService.update(this.item.idXepLoai, data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else {
      return;
    }
  }
}
