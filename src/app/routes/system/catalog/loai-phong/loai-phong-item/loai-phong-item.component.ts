import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { log } from '@delon/util';
import { ButtonModel } from '@model';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';
import { LoaiPhongService } from 'src/app/services/api/loai-phong.service';

@Component({
  selector: 'app-loai-phong-item',
  templateUrl: './loai-phong-item.component.html',
  styleUrls: ['./loai-phong-item.component.less']
})
export class LoaiPhongItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  isInfo = false;
  isEdit = false;
  isAdd = false;
  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  public Editor = ClassicEditor;

  config = {
    toolbar: {
      shouldNotGroupWhenFull: true
    }
  };

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private loaiPhongApiService: LoaiPhongService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,

      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: this.i18n.fanyi('app.common.button.edit'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      MaLoaiPhong: [null, [Validators.required]],
      TenLoaiPhong: [null, [Validators.required]],
      ThucHanh: [null]
    });
  }

  onEditorReady(editor: any): void {
    log('Editor is ready to use!', editor);
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.initRightOfUser();
  }

  initRightOfUser(): void {
    this.btnSave.grandAccess = this.aclService.canAbility('LOAI_PHONG_ADD') || this.aclService.canAbility('LOAI_PHONG_EDIT');
    this.btnEdit.grandAccess = this.aclService.canAbility('LOAI_PHONG_EDIT');
  }

  //#region Update-form-type
  updateFormToAdd(): void {
    this.isInfo = false;
    this.isEdit = false;
    this.isAdd = true;
    this.tittle = this.i18n.fanyi('function.loai-phong.modal.title-add');
    this.item = {};
    this.form.get('MaLoaiPhong')?.enable();
    this.form.get('TenLoaiPhong')?.enable();
    this.form.get('ThucHanh')?.enable();
    this.form.get('ThucHanh')?.setValue(false);
  }

  updateFormToInfo(): void {
    this.isInfo = true;
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.loai-phong.modal.title-info');
    this.form.get('MaLoaiPhong')?.disable();
    this.form.get('TenLoaiPhong')?.disable();
    this.form.get('ThucHanh')?.disable();
  }

  updateFormToEdit(): void {
    this.isInfo = false;
    this.isEdit = true;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.loai-phong.modal.title-edit');
    this.form.get('MaLoaiPhong')?.enable();
    this.form.get('TenLoaiPhong')?.enable();
    this.form.get('ThucHanh')?.enable();
  }

  resetForm(): void {
    this.form.reset();
    this.form.get('isActive')?.setValue(true);
    this.form.get('order')?.setValue(1);
    this.form.get('isHighPriority')?.setValue(false);
  }

  updateDataToForm(data: any): void {
    this.form.get('MaLoaiPhong')?.setValue(data.maLoaiPhong);
    this.form.get('TenLoaiPhong')?.setValue(data.tenLoaiPhong);
    this.form.get('ThucHanh')?.setValue(data.thucHanh);
  }

  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.resetForm();
    this.isLoading = false;
    this.item = data;
    this.type = type;
    this.option = option;

    if (this.item?.idLoaiPhong) {
      this.getDataInfo(this.item.idLoaiPhong);
    }
    switch (type) {
      case FORM_TYPE.ADD:
        this.updateFormToAdd();
        break;
      case FORM_TYPE.INFO:
        this.updateFormToInfo();
        break;
      case FORM_TYPE.EDIT:
        this.updateFormToEdit();
        break;
      default:
        this.updateFormToAdd();
        break;
    }
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  getDataInfo(id: any): Subscription | undefined {
    this.isLoading = true;
    const rs = this.loaiPhongApiService.getById(id).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateDataToForm(res.data);
        console.log(res.data);
      },
      error: (err: any) => {
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      }
    });
    return rs;
  }

  save(): Subscription | undefined {
    this.isLoading = true;

    cleanForm(this.form);
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid) {
      this.isLoading = false;
      this.messageService.error(this.i18n.fanyi('app.common.form.dirty'));
      return;
    }

    const data = {
      idLoaiPhong: this.item.idLoaiPhong,
      maLoaiPhong: this.form.get('MaLoaiPhong')?.value,
      tenLoaiPhong: this.form.get('TenLoaiPhong')?.value,
      thucHanh: this.form.get('ThucHanh')?.value
    };
    console.log(data);
    if (this.isAdd) {
      const promise = this.loaiPhongApiService.create(data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }

          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else if (this.isEdit) {
      const promise = this.loaiPhongApiService.update(this.item.idLoaiPhong, data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else {
      return;
    }
  }
}
