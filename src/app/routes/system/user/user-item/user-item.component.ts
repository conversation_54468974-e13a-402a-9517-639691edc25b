import { DOCUMENT } from '@angular/common';
import { Component, EventEmitter, Inject, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { DA_SERVICE_TOKEN, ITokenService } from '@delon/auth';
import { ALAIN_I18N_TOKEN, SettingsService } from '@delon/theme';
import { ArrayService, log } from '@delon/util';
import { ButtonModel, GridModel, QueryFilerModel } from '@model';
import { HoSoChinhApiService, UserApiService } from '@service';
import { BtnCellRenderComponent, DateCellRenderComponent } from '@shared';
import { cleanForm, LIST_SEX } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-user-item',
  templateUrl: './user-item.component.html',
  styleUrls: ['./user-item.component.less']
})
export class UserItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  listIdentityNumberType: Array<{ value: string; label: string; displayName: string }> = [];
  form: FormGroup;
  moduleName = 'người dùng';
  excelStyles: any;
  listOfOrg: any = [];
  listSex = LIST_SEX;
  isInfo = false;
  isEdit = false;
  isAdd = false;
  checkSysAdmin = false;
  tittle = '';
  dateFormat = 'dd/MM/yyyy';
  isLoading = false;
  isReloadGrid = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  listCanBo: any = [];

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private userService: UserApiService,
    private aclService: ACLService,
    private notification: NzNotificationService,
    private hoSoChinhApiService: HoSoChinhApiService,
    private arrayService: ArrayService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    @Inject(DOCUMENT) private doc: any
  ) {
    this.btnSave = {
      title: 'Lưu',
      titlei18n: `${this.i18n.fanyi('layout.button.btn-save.label')}`,
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: 'Đóng',
      titlei18n: `${this.i18n.fanyi('layout.button.btn-cancel.label')}`,
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: 'Cập nhật',
      titlei18n: `${this.i18n.fanyi('layout.button.btn-edit.label')}`,
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit(this.item);
      }
    };

    this.form = this.fb.group({
      maCanBoUser: [''],
      maCanBo: [''],
      fullName: [''],
      userName: ['', [Validators.required]],
      password: [''],
      email: [''],
      isActive: [true]
    });
  }

  onCanBoChange(event: any): any {
    this.form.get('maCanBo')?.setValue('');
    this.form.get('fullName')?.setValue('');
    console.log(event);
    this.form.get('maCanBo')?.setValue(event);
    var canbo = this.listCanBo.find((r: any) => r.maCanBo === event);
    this.form.get('fullName')?.setValue(canbo?.hoTen);
  }

  handleCancel(): void {
    this.isVisible = false;
    if (this.isReloadGrid) {
      this.eventEmmit.emit({ type: 'success' });
    } else {
      this.eventEmmit.emit({ type: 'close' });
    }
  }

  ngOnInit(): void {
    this.initRightOfUser();
  }

  initRightOfUser(): void {
    this.btnEdit.grandAccess = this.aclService.canAbility('QUAN_LY_NGUOI_DUNG_EDIT');
    this.btnSave.grandAccess =
      this.aclService.canAbility('QUAN_LY_NGUOI_DUNG_ADD') || this.aclService.canAbility('QUAN_LY_NGUOI_DUNG_EDIT');
  }

  updateFormToAdd(): void {
    this.resetForm();
    this.isInfo = false;
    this.isEdit = false;
    this.isAdd = true;
    this.btnSave.visible = true;
    // this.tittle = `Thêm mới ${this.moduleName}`;
    this.tittle = `${this.i18n.fanyi('function.user.modal-item.header-add')}`;

    this.form.get('maCanBoUser')?.enable();
    this.form.get('fullName')?.enable();
    this.form.get('userName')?.enable();
    this.form.get('password')?.enable();
    this.form.get('email')?.enable();
    this.form.get('isActive')?.enable();

    this.form.get('isActive')?.setValue(true);
  }

  updateFormToEdit(data: any): void {
    this.isInfo = false;
    this.isEdit = true;
    this.isAdd = false;
    this.btnSave.visible = true;
    this.tittle = `${this.i18n.fanyi('function.user.modal-item.header-edit')}`;

    this.form.get('maCanBoUser')?.setValue(data?.maCanBoUser);
    this.form.get('maCanBo')?.setValue(data?.maCanBoUser);
    this.form.get('fullName')?.setValue(data?.fullName);
    this.form.get('userName')?.setValue(data?.userName);
    this.form.get('password')?.setValue(data?.password);
    this.form.get('email')?.setValue(data?.email);
    this.form.get('isActive')?.setValue(data?.isActive);

    this.form.get('maCanBoUser')?.enable();
    this.form.get('fullName')?.enable();
    this.form.get('userName')?.disable();
    this.form.get('maCanBo')?.disable();
    this.form.get('password')?.enable();
    this.form.get('email')?.enable();
    this.form.get('isActive')?.enable();
  }

  updateFormToInfo(data: any): void {
    this.isInfo = true;
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = `${this.i18n.fanyi('function.user.model-item-header-info')}`;
    this.form.get('maCanBoUser')?.setValue(data?.maCanBoUser);
    this.form.get('fullName')?.setValue(data?.fullName);
    this.form.get('userName')?.setValue(data?.userName);
    // this.form.get('password')?.setValue(data?.password);
    this.form.get('email')?.setValue(data?.email);
    this.form.get('isActive')?.setValue(data?.isActive);
    this.form.get('userName')?.disable();
    this.form.disable();
  }

  updateFormType(type: string, data: any = {}): void {
    switch (type) {
      case 'add':
        this.updateFormToAdd();
        break;
      case 'info':
        this.updateFormToInfo(data);
        break;
      case 'edit':
        this.updateFormToEdit(data);
        break;
      default:
        this.updateFormToAdd();
        break;
    }
  }

  getUserById(id: any, type: any): any {
    const rs = this.userService.getById(id).subscribe(
      (res: any) => {
        if (res.code !== 200) {
          this.messageService.error(`Có lỗi xảy ra ${res.message}`);
          return;
        }
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`Có lỗi xảy ra ${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateFormType(type, res.data);
      },
      (err: any) => {
        console.log(err);
      }
    );
    return rs;
  }

  public initData(data: any, type: any = null, option: any = {}): void {
    this.item = data;
    if (data.userId) {
      const rs = this.getUserById(data.userId, type);
    } else {
      this.updateFormToAdd();
    }
    this.isLoading = false;
    this.isReloadGrid = false;
    this.type = type;
    this.option = option;
    this.isVisible = true;
    if (this.listCanBo.length === 0) {
      this.initListCanBo();
    }
  }

  resetForm(): void {
    this.form.reset();
    this.form.reset();
    this.form.get('isActive')?.setValue(true);
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: 'success' });
  }

  onModalEventEmmit(event: any) {
    console.log(event);
  }

  save(isCreateAfter: boolean = false): Subscription | undefined {
    this.isLoading = true;

    cleanForm(this.form);
    // tslint:disable-next-line:forin
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid || !this.form.valid) {
      this.isLoading = false;
      // this.messageService.error(`${this.i18n.fanyi('function.user.modal-item.error.message.form-invalid')}`);
      return;
    }

    const data = {
      userId: this.item?.userId,
      maCanBoUser: this.form.get('maCanBoUser')?.value,
      fullName: this.form.get('fullName')?.value,
      userName: this.form.get('userName')?.value,
      password: this.form.get('password')?.value,
      email: this.form.get('email')?.value,
      isActive: this.form.get('isActive')?.value
    };

    if (this.isAdd) {
      const promise = this.userService.create(data).subscribe(
        (res: any) => {
          this.isLoading = false;
          if (res.code !== 200) {
            this.messageService.error(`${res.message}`);
            return;
          }
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          const dataResult = res.data;
          this.messageService.success(`${res.message}`);
          this.isReloadGrid = true;
          if (isCreateAfter) {
            this.resetForm();
          } else {
            this.closeModalReloadData();
          }
        },
        (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        }
      );
      return promise;
    } else if (this.isEdit) {
      const promise = this.userService.update(this.item.userId, data).subscribe(
        (res: any) => {
          this.isLoading = false;
          if (res.code !== 200) {
            this.messageService.error(`${res.message}`);
            return;
          }
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          const dataResult = res.data;
          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        }
      );
      return promise;
    } else {
      return;
    }
  }

  initListCanBo() {
    this.hoSoChinhApiService.getListCombobox().subscribe(
      res => {
        this.listCanBo = res.data;
        this.listCanBo.forEach((element: any) => {
          element.value = element.maCanBo;
          element.label = element.hoTen;
        });
      },
      err => {}
    );
  }
}
